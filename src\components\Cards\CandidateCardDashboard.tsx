"use client";

import { MessageSquareDot, Trash2 } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { toast } from "sonner";
import { CurrencyIcon, LocationIcon1 } from "../Icons";
import LoadingSpinner from "@/components/LoadingSpinner/LoadingSpinner";
import { useCreateConversation, useUnsaveCandidate } from "@/hooks/useMutation";

interface CandidateCardDashboardProps {
  candidateId: string;
  candidateImage: string;
  candidateName: string;
  candidateProfessional: string;
  candidateLocation: string;
  candidateSalary: string;
  candidateDescription: string;
  link?: string; // Made optional since we're not using it anymore
  isProMember?: boolean;
  _savedId?: string;
}

const CandidateCardDashboard: React.FC<CandidateCardDashboardProps> = ({
  candidateId,
  candidateImage,
  candidateName,
  candidateProfessional,
  candidateLocation,
  candidateSalary,
  candidateDescription,
  isProMember = false,
  // Removed unused props: link, _savedId
}) => {
  const router = useRouter();
  const [isCreatingConversation, setIsCreatingConversation] = useState(false);
  const { mutateAsync: unsaveCandidate, isPending: isRemoving } = useUnsaveCandidate();

  // Create conversation mutation
  const { mutate: createConversation } = useCreateConversation({
    onSuccess: (data) => {
      toast.success(data.message || "Conversation created successfully");

      // The useCreateConversation hook will automatically join the conversation
      // via socket, which will trigger the join_conversation event

      // Navigate to the message page with the new conversation ID
      router.push(`/message?id=${data.data._id}`);
      setIsCreatingConversation(false);
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to create conversation");
      setIsCreatingConversation(false);
    },
  });

  const handleRemoveCandidate = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!candidateId) {
      toast.error("Cannot remove candidate: Missing candidate ID");
      return;
    }

    try {
      await unsaveCandidate(candidateId);
    } catch {
      // Error handled by toast
    }
  };

  // Handle message action
  const handleCreateConversation = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!candidateId) {
      toast.error("Cannot create conversation: Missing candidate ID");
      return;
    }

    setIsCreatingConversation(true);
    createConversation({
      type: "direct",
      jobSeekerProfileId: candidateId, // Using candidateId as jobApplicationId for direct messages
    });
  };
  return (
    <div className="relative p-6 border border-gray-300 hover:border-orange-100 transition-all rounded-2xl shadow-md">
      <div className="relative mb-4">
        <Image
          src={candidateImage}
          alt={candidateName}
          className="w-[100px] h-[100px] rounded-full mx-auto"
          width={100}
          height={100}
        />
        {isProMember && (
          <span className="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              fill="none"
              viewBox="0 0 30 30"
            >
              <rect width="30" height="30" fill="#007AB5" rx="15"></rect>
              <path
                fill="#fff"
                d="M21.431 11.6a.99.99 0 0 0-1.181.237l-2.104 2.268-2.238-5.018V9.08a1 1 0 0 0-1.816 0v.006l-2.238 5.018-2.104-2.268a1 1 0 0 0-1.73.859l1.418 6.492a1 1 0 0 0 .982.812h9.16a1 1 0 0 0 .983-.812l1.417-6.492q-.001-.01.004-.02a.99.99 0 0 0-.553-1.076m-1.847 7.38-.003.02h-9.162l-.003-.02L9 12.5l.009.01 2.625 2.828a.5.5 0 0 0 .823-.137L15 9.5l2.543 5.703a.5.5 0 0 0 .824.136l2.625-2.827L21 12.5z"
              ></path>
            </svg>
          </span>
        )}
        <button
          onClick={handleRemoveCandidate}
          disabled={isRemoving}
          className="absolute top-0 right-0 p-1 rounded-full hover:bg-red-50 transition-colors"
          aria-label="Remove from saved candidates"
        >
          <Trash2 size={20} className={`text-red-500 ${isRemoving ? "opacity-50" : ""}`} />
        </button>
      </div>
      <div>
        <div className="text-center">
          <div className="mb-3">
            <h2 className="text-2xl font-bold text-black-100">{candidateName}</h2>
          </div>
          <p className="text-orange-100 font-medium">{candidateProfessional}</p>
        </div>
      </div>
      <div className="flex my-6">
        <div className="text-black-100 flex gap-x-2">
          <LocationIcon1 />
          {candidateLocation}
        </div>
        <div className="text-black-100 flex gap-x-2 ml-6">
          <CurrencyIcon />
          {candidateSalary}
        </div>
      </div>

      <div className="mb-4">
        <p className="text-black-100 text-base ">{candidateDescription}</p>
      </div>
      <button
        onClick={handleCreateConversation}
        disabled={isCreatingConversation}
        className="w-full bg-orange-100 rounded-full text-white p-4 flex justify-center items-center gap-x-2"
      >
        {isCreatingConversation ? (
          <>
            <LoadingSpinner size="small" /> Creating chat...
          </>
        ) : (
          <>
            Message <MessageSquareDot />
          </>
        )}
      </button>
    </div>
  );
};

export default CandidateCardDashboard;
