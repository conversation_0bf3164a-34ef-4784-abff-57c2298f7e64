"use client";

import { useEffect, useState } from "react";
import JobShortCardDashboard from "@/components/Cards/JobShortCardDashboard";
import LoadingSpinner from "@/components/LoadingSpinner/LoadingSpinner";
import Pagination from "@/components/Pagination/Pagination";
import JobNavigation from "@/components/Sections/JobNavigation";
import { useGetMyJobApplications } from "@/hooks/useQuery";
import { formatDate } from "@/utils/formatDate";
import { formatSalaryRange } from "@/utils/formatSalaryRange";

const navItems = [
  { href: "/dashboard/applied-jobs", label: "Applied Jobs" },
  { href: "/dashboard/shortlisted-jobs", label: "Shortlisted Jobs" },
  { href: "/dashboard/saved-jobs", label: "Saved Jobs" },
];

export default function AppliedJobsPage() {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  const { data, isLoading, isError, refetch } = useGetMyJobApplications(
    { page, limit },
    { refetchOnWindowFocus: false }
  );

  useEffect(() => {
    refetch();
  }, [page, refetch]);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <JobNavigation navItems={navItems} />
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="space-y-6">
        <JobNavigation navItems={navItems} />
        <div className="flex justify-center items-center h-64">
          <p className="text-red-500">Error loading job applications. Please try again later.</p>
        </div>
      </div>
    );
  }

  const applications = data?.data?.applications || [];
  const pagination = data?.data?.pagination;
  console.log(applications);

  return (
    <div className="space-y-6">
      <JobNavigation navItems={navItems} />

      {applications.length === 0 ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-gray-500">No job applications found.</p>
        </div>
      ) : (
        <>
          {applications.map((application) => {
            const job = application.job;
            if (!job) return null;

            const jobCategory = job.jobCategory || "";
            const cityName = job.location?.city || "Remote";
            const companyName = job.recruiterProfile?.companyProfile?.companyName || "Company";
            const deadline = formatDate(job.applicationDeadline);
            const imageUrl =
              job.recruiterProfile?.companyProfile?.profilePicture || "/images/company-logo2.png";
            const jobTitle = job.jobTitle || "Job Title";
            const jobType = job.jobType || "";
            const salaryRange = formatSalaryRange(
              job.salaryType,
              job.salaryRangeStart,
              job.salaryRangeEnd
            );
            const status = application.status;

            return (
              <JobShortCardDashboard
                key={application._id}
                jobId={job._id}
                category={jobCategory}
                cityName={cityName}
                companyName={companyName}
                deadline={deadline}
                imageUrl={imageUrl}
                jobTitle={jobTitle}
                jobType={jobType}
                salaryRange={salaryRange}
                shortlisted={status === "SHORTLISTED"}
                rejected={status === "REJECTED"}
                pending={status === "PENDING"}
                isSaved={job.isSaved}
                isBoosted={job.isBoosted}
                isPremium={job.isPremium}
                isDeleted={job.isDeleted}
                isSavedBtnShow={false}
              />
            );
          })}

          {pagination && pagination.pages > 1 && (
            <div className="flex justify-center mt-8">
              <Pagination
                currentPage={page}
                totalPages={pagination.pages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
