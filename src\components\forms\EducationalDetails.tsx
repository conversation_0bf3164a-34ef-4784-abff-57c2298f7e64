"use client";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { toast } from "sonner";
import CandidateInfo from "../Cards/CandidateInfo";
import { MortarboardIcon } from "../Icons";
import EducationalDetailsForm from "./EducationalDetailsForm";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useUpdateJobSeekerProfile } from "@/hooks/useMutation";
import { useGetJobSeekerProfile } from "@/hooks/useQuery";
import { ApiError } from "@/types/common.types";
import { IAcademicExperience } from "@/types/query.types";

const EducationalDetails = () => {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [editingExperience, setEditingExperience] = useState<IAcademicExperience | null>(null);
  const queryClient = useQueryClient();
  const { data: profileData, isPending } = useGetJobSeekerProfile();
  const { mutate: updateProfile } = useUpdateJobSeekerProfile({
    onSuccess: () => {
      toast.success("Profile updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-jobseeker-profile"] });
    },
    onError: (error) => {
      console.log({ error });
      toast.error(error.response?.data?.message || "Failed to update profile");
    },
  });

  const handleClose = () => {
    setOpen(false);
    setEditingExperience(null);
  };

  const handleSuccess = async (formData: IAcademicExperience[]) => {
    try {
      const existingExperiences = profileData?.data.academicExperiences || [];
      const updatedExperiences = editingExperience
        ? existingExperiences.map((exp) => (exp._id === editingExperience._id ? formData[0] : exp))
        : [...existingExperiences, ...formData];

      updateProfile({
        academicExperiences: updatedExperiences as IAcademicExperience[],
      });
      handleClose();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to save educational details";
      toast.error(errorMessage);
    }
  };

  const handleError = (error: unknown) => {
    const errorMessage =
      error instanceof Error ? error.message : "Failed to save educational details";
    toast.error(errorMessage);
  };

  const handleEdit = (experience: IAcademicExperience) => {
    setEditingExperience(experience);
    setOpen(true);
  };

  const handleDelete = async (experienceId: string) => {
    try {
      const existingExperiences = profileData?.data.academicExperiences || [];
      const updatedExperiences = existingExperiences.filter((exp) => exp._id !== experienceId);

      updateProfile({
        academicExperiences: updatedExperiences,
      });
      toast.success("Experience deleted successfully");
    } catch (error) {
      toast.error((error as ApiError).response?.data.message || "Failed to delete experience");
    }
  };

  const academicExperiences = profileData?.data.academicExperiences || [];

  return (
    <div>
      <div className="sm:flex items-center justify-between mb-10">
        <div className="mb-4 sm:mb-0">
          <h2 className="text-blue-200 text-2xl font-bold">Educational Details</h2>
          <h3 className="text-4xl font-medium text-black-100 mt-3">Academic Experience</h3>
        </div>
        <div>
          <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger
              onClick={() => setEditingExperience(null)}
              className="border-orange-100 border rounded-full px-3 py-3 flex items-center gap-x-3 font-medium text-orange-100"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="30"
                height="30"
                fill="none"
                viewBox="0 0 30 30"
              >
                <path
                  stroke="#EC761E"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 29c7.732 0 14-6.268 14-14S22.732 1 15 1 1 7.268 1 15s6.268 14 14 14M10.333 15h9.334M15 10.333v9.333"
                ></path>
              </svg>
              Add Education
            </DialogTrigger>
            <DialogContent className="md:max-w-[920px] max-h-[80vh] overflow-y-auto">
              <DialogHeader className="text-left">
                <DialogTitle className="text-2xl text-orange-100 font-bold mb-5">
                  Educational Details
                </DialogTitle>
                <EducationalDetailsForm
                  goBack={handleClose}
                  onSuccess={handleSuccess}
                  onError={handleError}
                  defaultValues={editingExperience ? [editingExperience] : []}
                />
              </DialogHeader>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      <div className="space-y-4">
        {isPending ? (
          <div className="text-center text-gray-500 py-8">Loading educational details...</div>
        ) : academicExperiences.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            No educational details added yet. Click &quot;Add Education&quot; to get started.
          </div>
        ) : (
          academicExperiences.map((experience: IAcademicExperience, index: number) => (
            <div key={experience._id || index} className="relative">
              <CandidateInfo
                icon={<MortarboardIcon />}
                endDate={
                  experience.startDate && new Date(experience.startDate).toLocaleDateString()
                }
                startDate={experience.endDate && new Date(experience.endDate).toLocaleDateString()}
                text={experience.degree}
                title={experience.instituteName}
                onEdit={() => handleEdit(experience)}
                onDelete={() => handleDelete(experience._id)}
              />
            </div>
          ))
        )}

        <div className="flex gap-5">
          <div className={`flex flex-wrap gap-y-3 gap-x-5 mt-10`}>
            <button
              disabled={isPending}
              onClick={() => router.back()}
              type="submit"
              className="font-bold py-4 px-10 text-black font-base rounded-full inline-flex items-center justify-center space-x-2 bg-[#E7E7E7] "
            >
              Go Back
            </button>
          </div>
          <div className={`flex flex-wrap gap-y-3 gap-x-5 mt-10`}>
            <button
              disabled={isPending}
              onClick={() => router.push("?stepId=certifications")}
              className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
            >
              {isPending ? "Loading..." : "Save Details"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EducationalDetails;
