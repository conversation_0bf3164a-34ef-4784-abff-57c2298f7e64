import Image from "next/image";
import Link from "next/link";
import CTASection from "@/components/Sections/CTASection";
import MobileAppSection from "@/components/Sections/MobileAppSection";

export default function TermsAndConditions() {
  return (
    <>
      <header className="py-20 relative">
        <div className="absolute w-full top-0 left-0 z-[-1]">
          <Image
            alt="Background pattern"
            src="/images/spiral.png"
            width={837}
            height={1920}
            className="w-full"
            priority
          />
        </div>
        <div className="container mx-auto text-center">
          <h1 className="font-bold lg:text-5xl text-3xl leading-tight text-orange-100 mt-5 mx-auto">
            Terms and Conditions
          </h1>
          <p className="text-gray-100 py-6 max-w-3xl mx-auto">
            <time dateTime="2024-07-15">Last Updated: July 15, 2024</time>
          </p>
        </div>
      </header>

      <main className="pb-20">
        <div className="container mx-auto">
          <article className="bg-white rounded-2xl p-8 lg:p-12 shadow-sm">
            {/* Introduction */}
            <section className="mb-10">
              <h2 className="text-2xl font-bold text-black-100 mb-4">
                Introduction and Acceptance
              </h2>
              <p className="text-gray-100 mb-4">
                Welcome to <strong>YesJobs</strong>. These Terms and Conditions govern your use of
                the YesJobs website, mobile applications, and services (collectively, the
                &ldquo;Services&rdquo;).
              </p>
              <p className="text-gray-100">
                By accessing or using our Services, you agree to be bound by these Terms and
                Conditions. If you do not agree to these terms, please{" "}
                <em>do not use our Services</em>. We reserve the right to modify these terms at any
                time, and such modifications shall be effective immediately upon posting on our
                website.
              </p>
            </section>

            {/* User Accounts */}
            <section className="mb-10">
              <h2 className="text-2xl font-bold text-black-100 mb-4">
                User Accounts and Responsibilities
              </h2>
              <p className="text-gray-100 mb-4">
                To access certain features of our Services, you may need to create an account. When
                you create an account, you agree to:
              </p>
              <ul className="list-disc pl-6 text-gray-100 space-y-2">
                <li>Provide accurate, current, and complete information</li>
                <li>Maintain and promptly update your account information</li>
                <li>Keep your password secure and confidential</li>
                <li>Be responsible for all activities that occur under your account</li>
                <li>Notify us immediately of any unauthorized use of your account</li>
              </ul>
              <p className="text-gray-100 mt-4">
                We reserve the right to suspend or terminate your account if any information
                provided is inaccurate, false, or no longer current, or if we believe you have
                violated these Terms and Conditions.
              </p>
            </section>

            {/* Content and Conduct */}
            <section className="mb-10">
              <h2 className="text-2xl font-bold text-black-100 mb-4">Content and Conduct</h2>
              <p className="text-gray-100 mb-4">
                When using our Services, you agree <strong>not</strong> to:
              </p>
              <ul className="list-disc pl-6 text-gray-100 space-y-2">
                <li>
                  Post or transmit any content that is unlawful, harmful, threatening, abusive,
                  harassing, defamatory, vulgar, obscene, or otherwise objectionable
                </li>
                <li>
                  Impersonate any person or entity, or falsely state or otherwise misrepresent your
                  affiliation with a person or entity
                </li>
                <li>
                  Upload or transmit any content that infringes upon the rights of others, including
                  intellectual property rights
                </li>
                <li>Use our Services for any illegal or unauthorized purpose</li>
                <li>
                  Interfere with or disrupt our Services or servers or networks connected to our
                  Services
                </li>
                <li>Collect or store personal data about other users without their consent</li>
                <li>Post false, inaccurate, misleading, deceptive, or offensive content</li>
              </ul>
            </section>

            {/* Job Listings and Applications */}
            <section className="mb-10">
              <h2 className="text-2xl font-bold text-black-100 mb-4">
                Job Listings and Applications
              </h2>
              <p className="text-gray-100 mb-4">
                YesJobs provides a platform for employers to post job listings and for job seekers
                to apply for jobs. We <em>do not guarantee</em>:
              </p>
              <ul className="list-disc pl-6 text-gray-100 space-y-2">
                <li>The accuracy or completeness of job listings</li>
                <li>That any job listings will result in interviews or job offers</li>
                <li>The validity of any job application</li>
                <li>The qualifications or suitability of any job seeker or employer</li>
              </ul>
              <p className="text-gray-100 mt-4">
                Employers are solely responsible for the content of their job listings and for
                ensuring compliance with all applicable laws. Job seekers are solely responsible for
                the content of their applications and for verifying the legitimacy of job listings.
              </p>
            </section>

            {/* Email Communications */}
            <section className="mb-10">
              <h2 className="text-2xl font-bold text-black-100 mb-4">Email Communications</h2>
              <p className="text-gray-100 mb-4">
                By creating an account on YesJobs, you consent to receive transactional emails
                related to your account and activity on our platform. These may include but are not
                limited to:
              </p>
              <ul className="list-disc pl-6 text-gray-100 space-y-2">
                <li>Account verification and security notifications</li>
                <li>Job application confirmations and updates</li>
                <li>Messages from employers or job seekers</li>
                <li>Job recommendations based on your profile and preferences</li>
                <li>Service announcements and platform updates</li>
              </ul>
              <p className="text-gray-100 mt-4">
                You can manage your email preferences through your <em>account settings</em>.
                However, even if you opt out of promotional emails, we will still send you important
                transactional and relationship messages regarding your account and our services.
              </p>
              <p className="text-gray-100 mt-4">
                Each email we send will include a{" "}
                <strong>clear and conspicuous option to unsubscribe</strong> from future marketing
                communications. We will honor your opt-out requests promptly, typically within 14
                days.
              </p>
            </section>

            {/* Data Collection and Use */}
            <section className="mb-10">
              <h2 className="text-2xl font-bold text-black-100 mb-4">Data Collection and Use</h2>
              <p className="text-gray-100 mb-4">
                We collect and process personal data as described in our Privacy Policy. By using
                our Services, you consent to such processing and you warrant that all data provided
                by you is accurate and complete.
              </p>
              <p className="text-gray-100 mb-4">You acknowledge that:</p>
              <ul className="list-disc pl-6 text-gray-100 space-y-2">
                <li>
                  We may collect information about your use of our Services to improve our platform
                </li>
                <li>
                  Your profile information may be visible to other users of the platform as
                  specified in your privacy settings
                </li>
                <li>
                  We may use anonymized and aggregated data for analytical and statistical purposes
                </li>
                <li>
                  We implement reasonable security measures but cannot guarantee absolute security
                </li>
              </ul>
              <p className="text-gray-100 mt-4">
                For complete information on how we collect, use, and protect your data, please refer
                to our{" "}
                <Link href="/privacy-policy" className="text-orange-100 hover:underline">
                  Privacy Policy
                </Link>
                .
              </p>
            </section>

            {/* Intellectual Property */}
            <section className="mb-10">
              <h2 className="text-2xl font-bold text-black-100 mb-4">Intellectual Property</h2>
              <p className="text-gray-100 mb-4">
                The content, organization, graphics, design, compilation, and other matters related
                to our Services are protected by applicable copyrights, trademarks, and other
                proprietary rights. Copying, redistributing, using, or publishing any such content
                is <strong>strictly prohibited</strong> without our prior written consent.
              </p>
              <p className="text-gray-100">
                By submitting content to our Services, you grant us a worldwide, non-exclusive,
                royalty-free, perpetual, irrevocable, and fully sublicensable right to use,
                reproduce, modify, adapt, publish, translate, create derivative works from,
                distribute, and display such content in any media.
              </p>
            </section>

            {/* Limitation of Liability */}
            <section className="mb-10">
              <h2 className="text-2xl font-bold text-black-100 mb-4">Limitation of Liability</h2>
              <p className="text-gray-100 mb-4">
                To the maximum extent permitted by law, YesJobs and its affiliates, officers,
                employees, agents, partners, and licensors shall <strong>not be liable</strong> for
                any direct, indirect, incidental, special, consequential, or punitive damages,
                including without limitation, loss of profits, data, use, goodwill, or other
                intangible losses, resulting from:
              </p>
              <ul className="list-disc pl-6 text-gray-100 space-y-2">
                <li>Your access to or use of or inability to access or use our Services</li>
                <li>Any conduct or content of any third party on our Services</li>
                <li>Any content obtained from our Services</li>
                <li>Unauthorized access, use, or alteration of your transmissions or content</li>
              </ul>
            </section>

            {/* Termination */}
            <section className="mb-10">
              <h2 className="text-2xl font-bold text-black-100 mb-4">Termination</h2>
              <p className="text-gray-100">
                We may terminate or suspend your account and access to our Services immediately,
                without prior notice or liability, for any reason, including without limitation if
                you breach these Terms and Conditions. Upon termination, your right to use our
                Services will immediately cease.
              </p>
            </section>

            {/* Governing Law */}
            <section className="mb-10">
              <h2 className="text-2xl font-bold text-black-100 mb-4">Governing Law</h2>
              <p className="text-gray-100">
                These Terms and Conditions shall be governed by and construed in accordance with the
                laws of Australia, without regard to its conflict of law provisions. You agree to
                submit to the personal and exclusive jurisdiction of the courts located within
                Australia for the resolution of any disputes.
              </p>
            </section>

            {/* Changes to Terms */}
            <section className="mb-10">
              <h2 className="text-2xl font-bold text-black-100 mb-4">Changes to Terms</h2>
              <p className="text-gray-100">
                We reserve the right, at our sole discretion, to modify or replace these Terms and
                Conditions at any time. We will provide notice of any changes by posting the new
                Terms and Conditions on this page and updating the &ldquo;Last Updated&rdquo; date
                at the top. Your continued use of our Services after any such changes constitutes
                your acceptance of the new Terms and Conditions.
              </p>
            </section>

            {/* Contact Us */}
            <section>
              <h2 className="text-2xl font-bold text-black-100 mb-4">Contact Us</h2>
              <p className="text-gray-100 mb-4">
                If you have any questions or concerns about these Terms and Conditions, please
                contact us at:
              </p>
              <address className="text-gray-100 not-italic">
                <strong className="text-black-100">Email:</strong>
                <a
                  href="mailto:<EMAIL>"
                  className="text-orange-100 hover:underline ml-1"
                >
                  <EMAIL>
                </a>
                <br />
                <span className="font-medium text-black-100">Address:</span> The address is suite2
                Level 13 977 ann street
              </address>
            </section>
          </article>
        </div>
      </main>

      <CTASection />
      <MobileAppSection />
    </>
  );
}
