import Link from "next/link";

export default function RecentlyPostedHeader() {
  return (
    <div className="flex flex-wrap gap-3 justify-between mb-7">
      <div>
        <h2 className="text-blue-100 text-3xl font-bold mb-3">Recently Posted Jobs</h2>
        <p className="text-gray-100">View your company&apos;s recently posted jobs</p>
      </div>
      <div>
        <Link
          href={"/company-dashboard/post-job"}
          className="inline-flex justify-center items-center gap-x-2 rounded-full bg-blue-100 text-white px-10 py-3 text-lg font-bold"
        >
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="28"
              height="29"
              fill="none"
              viewBox="0 0 28 29"
            >
              <path
                stroke="#fff"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M14 25c5.799 0 10.5-4.701 10.5-10.5S19.799 4 14 4 3.5 8.701 3.5 14.5 8.201 25 14 25M9 14.5h10M14 9.5v10"
              ></path>
            </svg>
          </span>
          <span>Post a Job</span>
        </Link>
      </div>
    </div>
  );
}
