"use client";
import { useQueryClient } from "@tanstack/react-query";
import { X, DollarSign, MapPin } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState, useEffect, useCallback } from "react";
import { useForm, Controller } from "react-hook-form";
import { toast } from "sonner";
import usePlacesAutocomplete, { getGeocode, getLatLng } from "use-places-autocomplete";

import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

import { useJobPosting, useUpdateJob } from "@/hooks/useMutation";
import { useGetAllEnums, useGetJobById } from "@/hooks/useQuery";
import type { ICreateJobRequestDto } from "@/types/query.types";

interface JobPostingFormProps {
  jobId?: string;
}

export function JobPostingForm({ jobId }: JobPostingFormProps) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [tempSelectedSkills, setTempSelectedSkills] = useState<string[]>([]);
  const [isSkillsDialogOpen, setIsSkillsDialogOpen] = useState(false);
  const [coordinates, setCoordinates] = useState({ lat: 0, lng: 0 });

  // Places autocomplete
  const {
    ready,
    value,
    setValue: setLocationValue,
    suggestions: { status, data },
    clearSuggestions,
  } = usePlacesAutocomplete({
    requestOptions: {
      componentRestrictions: { country: "au" },
    },
  });

  // Fetch enums data
  const { data: enumsData, isLoading: isEnumsLoading } = useGetAllEnums();

  // Fetch job data if jobId is provided
  const { data: jobData, isLoading: isJobLoading } = useGetJobById(jobId || "", {
    enabled: !!jobId,
  });

  // Mutations for creating and updating jobs
  const { mutate: createJob, isPending: isCreating } = useJobPosting({
    onSuccess: (data) => {
      toast.success(data.message || "Job posted successfully");
      queryClient.invalidateQueries({ queryKey: ["get-recruiters-jobs"] });
      reset();
      setSelectedSkills([]);
      setTempSelectedSkills([]);
      setLocationValue("");
      router.push("/company-dashboard/all-jobs");
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to post job");
    },
  });

  const { mutate: updateJob, isPending: isUpdating } = useUpdateJob({
    onSuccess: (data) => {
      toast.success(data.message || "Job updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-recruiters-jobs"] });
      queryClient.invalidateQueries({ queryKey: ["get-job-by-id", jobId] });
      router.push("/company-dashboard/all-jobs");
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to update job");
    },
  });

  const isPending = isCreating || isUpdating;

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<ICreateJobRequestDto>({
    defaultValues: {
      jobTitle: "",
      jobDescription: "",
      applicationDeadline: new Date().toISOString(),
      jobCategory: "",
      jobType: "",
      jobMode: "",
      salaryType: "",
      salaryRangeStart: 0,
      salaryRangeEnd: 0,
      experienceLevel: "",
      qualification: "",
      careerLevel: "",
      location: {
        type: "Point",
        coordinates: [0, 0],
        formattedAddress: "",
        city: "",
        state: "",
        country: "",
      },
      keyResponsibilities: "",
      skillsAndExperience: "",
      skillsTag: [],
      isJobActive: true,
    },
  });

  // Initialize form with job data when available
  useEffect(() => {
    if (jobId && jobData?.data) {
      const job = jobData.data;

      // First reset the form with all values
      reset(
        {
          jobTitle: job.jobTitle || "",
          jobDescription: job.jobDescription || "",
          applicationDeadline: job.applicationDeadline || new Date().toISOString(),
          jobCategory: job.jobCategory || "",
          jobType: job.jobType || "",
          jobMode: job.jobMode || "",
          salaryType: job.salaryType || "",
          salaryRangeStart: job.salaryRangeStart || 0,
          salaryRangeEnd: job.salaryRangeEnd || 0,
          experienceLevel: job.experienceLevel || "",
          qualification: job.qualification || "",
          careerLevel: job.careerLevel || "",
          keyResponsibilities: job.keyResponsibilities || "",
          skillsAndExperience: job.skillsAndExperience || "",
          skillsTag: job.skillsTag || [],
          isJobActive: job.isJobActive !== undefined ? job.isJobActive : true,
          location: job.location || {
            type: "Point",
            coordinates: [0, 0],
            formattedAddress: "",
            city: "",
            state: "",
            country: "",
          },
        },
        { keepDefaultValues: false }
      );

      // Then explicitly set each select field individually to ensure they're properly initialized
      // This is necessary because shadcn Select components sometimes don't update properly with just reset
      setTimeout(() => {
        setValue("jobCategory", job.jobCategory || "");
        setValue("jobType", job.jobType || "");
        setValue("jobMode", job.jobMode || "");
        setValue("salaryType", job.salaryType || "");
        setValue("experienceLevel", job.experienceLevel || "");
        setValue("qualification", job.qualification || "");
        setValue("careerLevel", job.careerLevel || "");
      }, 0);

      if (job.location) {
        setLocationValue(job.location.formattedAddress || "");
        if (job.location.coordinates && job.location.coordinates.length === 2) {
          setCoordinates({
            lng: job.location.coordinates[0],
            lat: job.location.coordinates[1],
          });
        }
      }

      if (job.skillsTag && Array.isArray(job.skillsTag)) {
        setSelectedSkills(job.skillsTag);
        setTempSelectedSkills(job.skillsTag);
      }
    }
  }, [jobId, jobData, reset, setLocationValue, setValue]);

  // Handle location selection
  const handleSelectLocation = async (address: string) => {
    setLocationValue(address, false);
    clearSuggestions();

    try {
      const results = await getGeocode({ address });
      const { lat, lng } = getLatLng(results[0]);

      setCoordinates({ lat, lng });

      setValue("location", {
        type: "Point",
        coordinates: [lng, lat],
        formattedAddress: address,
        city: "",
        state: "",
        country: "",
      });
    } catch {
      toast.error("Failed to get location details");
    }
  };

  const handleAddSkill = useCallback((skill: string) => {
    setTempSelectedSkills((prev) =>
      prev.includes(skill) ? prev.filter((s) => s !== skill) : [...prev, skill]
    );
  }, []);

  const handleRemoveSkill = useCallback((skillToRemove: string) => {
    setSelectedSkills((prev) => prev.filter((skill) => skill !== skillToRemove));
    setTempSelectedSkills((prev) => prev.filter((skill) => skill !== skillToRemove));
  }, []);

  const handleConfirmSkillSelection = () => {
    setSelectedSkills(tempSelectedSkills);
    setIsSkillsDialogOpen(false);
  };

  const handleCancelSkillSelection = () => {
    setTempSelectedSkills(selectedSkills);
    setIsSkillsDialogOpen(false);
  };

  const onSubmit = (data: ICreateJobRequestDto) => {
    const formData = {
      ...data,
      skillsTag: selectedSkills,
      location: {
        ...data.location,
        coordinates: [coordinates.lng, coordinates.lat] as [number, number],
      },
    };

    if (jobId) {
      updateJob({ id: jobId, data: formData });
    } else {
      createJob(formData);
    }
  };

  if (isEnumsLoading || (jobId && isJobLoading)) {
    return <div>Loading...</div>;
  }

  const jobCategoriesEnum = enumsData?.data.JOB_CATEGORIES_ENUM || {};
  const jobTypesEnum = enumsData?.data.JOB_TYPE_ENUM || {};
  const jobModesEnum = enumsData?.data.JOB_MODE_ENUM || {};
  const salaryTypesEnum = enumsData?.data.SALARY_TYPE_ENUM || {};
  const experienceLevelsEnum = enumsData?.data.EXPERIENCE_RANGE_ENUM || {};
  const qualificationsEnum = enumsData?.data.QUALIFICATION_ENUM || {};
  const careerLevelsEnum = enumsData?.data.CAREER_LEVEL_ENUM || {};
  // Cast skillsEnum to allow string indexing
  const skillsEnum: Record<string, string[]> = enumsData?.data.SKILLS_ENUM || {};

  const inputStyles = "h-[60px] px-4 rounded-full border border-gray-300 shadow w-full";

  // Get job categories for skills tabs
  const jobCategories = Object.keys(skillsEnum).map((key) => ({
    label: key.replace(/_/g, " "),
    value: key,
  }));

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8 lg:w-[70%]">
      <div className="space-y-8">
        <div>
          <Label htmlFor="jobTitle" className="mb-5 block text-black-100 font-medium">
            Job Title
          </Label>
          <Controller
            name="jobTitle"
            control={control}
            rules={{ required: "Job Title is required" }}
            render={({ field }) => (
              <Input
                id="jobTitle"
                className={`${inputStyles} lg:w-[375px]`}
                placeholder="Enter job title"
                {...field}
              />
            )}
          />
          {errors.jobTitle && (
            <p className="text-red-500 text-sm mt-1">{errors.jobTitle.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="jobDescription" className="mb-5 block text-black-100 font-medium">
            Job Description
          </Label>
          <Controller
            name="jobDescription"
            control={control}
            rules={{ required: "Job Description is required" }}
            render={({ field }) => (
              <Textarea
                id="jobDescription"
                className={`${inputStyles} h-[120px] rounded-[20px]`}
                placeholder="Enter job description"
                {...field}
              />
            )}
          />
          {errors.jobDescription && (
            <p className="text-red-500 text-sm mt-1">{errors.jobDescription.message}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <Label htmlFor="applicationDeadline" className="mb-5 block text-black-100 font-medium">
            Application Deadline Date
          </Label>
          <Controller
            name="applicationDeadline"
            control={control}
            rules={{
              required: "Application Deadline Date is required",
              validate: (value) => new Date(value) > new Date() || "Deadline must be a future date",
            }}
            render={({ field: { value, onChange, ...field } }) => (
              <Input
                id="applicationDeadline"
                className={`${inputStyles} w-full block`}
                type="date"
                value={value ? new Date(value).toISOString().split("T")[0] : ""}
                onChange={(e) => onChange(new Date(e.target.value).toISOString())}
                {...field}
              />
            )}
          />
          {errors.applicationDeadline && (
            <p className="text-red-500 text-sm mt-1">{errors.applicationDeadline.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="jobCategory" className="mb-5 block text-black-100 font-medium">
            Job Category
          </Label>
          <Controller
            name="jobCategory"
            control={control}
            rules={{ required: "Job Category is required" }}
            render={({ field: { onChange, value } }) => (
              <Select value={value || undefined} onValueChange={onChange} defaultValue={undefined}>
                <SelectTrigger className={`${inputStyles}`}>
                  <SelectValue placeholder="Select job category" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(jobCategoriesEnum).map(([key, val]) => (
                    <SelectItem key={key} value={key}>
                      {String(val)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.jobCategory && (
            <p className="text-red-500 text-sm mt-1">{errors.jobCategory.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="jobType" className="mb-5 block text-black-100 font-medium">
            Job Type
          </Label>
          <Controller
            name="jobType"
            control={control}
            rules={{ required: "Job Type is required" }}
            render={({ field: { onChange, value } }) => (
              <Select value={value || undefined} onValueChange={onChange} defaultValue={undefined}>
                <SelectTrigger className={`${inputStyles}`}>
                  <SelectValue placeholder="Select job type" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(jobTypesEnum).map(([key, val]) => (
                    <SelectItem key={key} value={key}>
                      {String(val)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.jobType && <p className="text-red-500 text-sm mt-1">{errors.jobType.message}</p>}
        </div>

        <div>
          <Label htmlFor="jobMode" className="mb-5 block text-black-100 font-medium">
            Job Mode
          </Label>
          <Controller
            name="jobMode"
            control={control}
            rules={{ required: "Job Mode is required" }}
            render={({ field: { onChange, value } }) => (
              <Select value={value || undefined} onValueChange={onChange} defaultValue={undefined}>
                <SelectTrigger className={`${inputStyles}`}>
                  <SelectValue placeholder="Select job mode" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(jobModesEnum).map(([key, val]) => (
                    <SelectItem key={key} value={key}>
                      {String(val)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.jobMode && <p className="text-red-500 text-sm mt-1">{errors.jobMode.message}</p>}
        </div>

        <div>
          <Label htmlFor="salaryType" className="mb-5 block text-black-100 font-medium">
            Salary Type
          </Label>
          <Controller
            name="salaryType"
            control={control}
            rules={{ required: "Salary Type is required" }}
            render={({ field: { onChange, value } }) => (
              <Select value={value || undefined} onValueChange={onChange} defaultValue={undefined}>
                <SelectTrigger className={`${inputStyles}`}>
                  <SelectValue placeholder="Select salary type" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(salaryTypesEnum).map(([key, val]) => (
                    <SelectItem key={key} value={key}>
                      {String(val)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.salaryType && (
            <p className="text-red-500 text-sm mt-1">{errors.salaryType.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label className="mb-5 block text-black-100 font-medium">Salary Range</Label>
          <div className="flex space-x-4">
            <div className="relative flex-1">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Controller
                name="salaryRangeStart"
                control={control}
                rules={{
                  required: "Minimum Salary is required",
                  min: { value: 0, message: "Salary cannot be negative" },
                  validate: {
                    isNumber: (value) => !isNaN(value) || "Please enter a valid number",
                  },
                }}
                render={({ field: { value, onChange, ...field } }) => (
                  <Input
                    id="salaryRangeStart"
                    placeholder="Min"
                    className={`${inputStyles} pl-10`}
                    type="number"
                    min="0"
                    value={value.toString()}
                    onChange={(e) => {
                      const newValue = Number(e.target.value);
                      onChange(newValue);
                    }}
                    {...field}
                  />
                )}
              />
            </div>
            <div className="relative flex-1">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Controller
                name="salaryRangeEnd"
                control={control}
                rules={{
                  required: "Maximum Salary is required",
                  min: { value: 0, message: "Salary cannot be negative" },
                  validate: {
                    isNumber: (value) => !isNaN(value) || "Please enter a valid number",
                    isGreaterThanStart: (value) => {
                      const startValue = control._getWatch("salaryRangeStart");
                      return (
                        value > startValue || "Maximum salary must be greater than minimum salary"
                      );
                    },
                  },
                }}
                render={({ field: { value, onChange, ...field } }) => (
                  <Input
                    id="salaryRangeEnd"
                    placeholder="Max"
                    className={`${inputStyles} pl-10`}
                    type="number"
                    min="0"
                    value={value.toString()}
                    onChange={(e) => {
                      const newValue = Number(e.target.value);
                      onChange(newValue);
                    }}
                    {...field}
                  />
                )}
              />
            </div>
          </div>
          {(errors.salaryRangeStart || errors.salaryRangeEnd) && (
            <p className="text-red-500 text-sm mt-1">
              {errors.salaryRangeStart?.message || errors.salaryRangeEnd?.message}
            </p>
          )}
        </div>

        <div>
          <Label htmlFor="experienceLevel" className="mb-5 block text-black-100 font-medium">
            Experience
          </Label>
          <Controller
            name="experienceLevel"
            control={control}
            rules={{ required: "Experience is required" }}
            render={({ field: { onChange, value } }) => (
              <Select value={value || undefined} onValueChange={onChange} defaultValue={undefined}>
                <SelectTrigger className={`${inputStyles}`}>
                  <SelectValue placeholder="Select experience level" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(experienceLevelsEnum).map(([key, val]) => (
                    <SelectItem key={key} value={key}>
                      {String(val)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.experienceLevel && (
            <p className="text-red-500 text-sm mt-1">{errors.experienceLevel.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="qualification" className="mb-5 block text-black-100 font-medium">
            Qualification
          </Label>
          <Controller
            name="qualification"
            control={control}
            rules={{ required: "Qualification is required" }}
            render={({ field: { onChange, value } }) => (
              <Select value={value || undefined} onValueChange={onChange} defaultValue={undefined}>
                <SelectTrigger className={`${inputStyles}`}>
                  <SelectValue placeholder="Select qualification" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(qualificationsEnum).map(([key, val]) => (
                    <SelectItem key={key} value={key}>
                      {String(val)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.qualification && (
            <p className="text-red-500 text-sm mt-1">{errors.qualification.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="careerLevel" className="mb-5 block text-black-100 font-medium">
            Career Level
          </Label>
          <Controller
            name="careerLevel"
            control={control}
            rules={{ required: "Career Level is required" }}
            render={({ field: { onChange, value } }) => (
              <Select value={value || undefined} onValueChange={onChange} defaultValue={undefined}>
                <SelectTrigger className={`${inputStyles}`}>
                  <SelectValue placeholder="Select career level" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(careerLevelsEnum).map(([key, val]) => (
                    <SelectItem key={key} value={key}>
                      {String(val)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
          {errors.careerLevel && (
            <p className="text-red-500 text-sm mt-1">{errors.careerLevel.message}</p>
          )}
        </div>

        {/* Location Input - Using Google Places Autocomplete */}
        <div className="col-span-1 md:col-span-2">
          <Label htmlFor="location" className="mb-5 block text-black-100 font-medium">
            Location
          </Label>
          <div className="relative">
            <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
            <Input
              id="location"
              className={`${inputStyles} pl-10`}
              placeholder="e.g., New York, Remote"
              value={value}
              onChange={(e) => setLocationValue(e.target.value)}
              disabled={!ready}
            />
            {status === "OK" && (
              <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded-lg mt-2 max-h-60 overflow-y-auto">
                {data.map(({ place_id, description }) => (
                  <li
                    key={place_id}
                    className="p-2 cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSelectLocation(description)}
                  >
                    {description}
                  </li>
                ))}
              </ul>
            )}
          </div>
          {errors.location?.formattedAddress && (
            <p className="text-red-500 text-sm mt-1">{errors.location.formattedAddress.message}</p>
          )}
        </div>
      </div>

      <div className="space-y-8">
        <div>
          <Label htmlFor="keyResponsibilities" className="mb-5 block text-black-100 font-medium">
            Key Responsibilities
          </Label>
          <Controller
            name="keyResponsibilities"
            control={control}
            rules={{ required: "Key Responsibilities are required" }}
            render={({ field }) => (
              <Textarea
                id="keyResponsibilities"
                className={`${inputStyles} h-[120px] rounded-[20px]`}
                placeholder="Enter key responsibilities"
                {...field}
              />
            )}
          />
          {errors.keyResponsibilities && (
            <p className="text-red-500 text-sm mt-1">{errors.keyResponsibilities.message}</p>
          )}
        </div>

        <div>
          <Label htmlFor="skillsAndExperience" className="mb-5 block text-black-100 font-medium">
            Skills & Experience
          </Label>
          <Controller
            name="skillsAndExperience"
            control={control}
            rules={{ required: "Skills & Experience are required" }}
            render={({ field }) => (
              <Textarea
                id="skillsAndExperience"
                className={`${inputStyles} h-[120px] rounded-[20px]`}
                placeholder="Enter required skills and experience"
                {...field}
              />
            )}
          />
          {errors.skillsAndExperience && (
            <p className="text-red-500 text-sm mt-1">{errors.skillsAndExperience.message}</p>
          )}
        </div>

        {/* Skills Selection - Matching ProfessionalSkills component */}
        <Dialog open={isSkillsDialogOpen} onOpenChange={setIsSkillsDialogOpen}>
          <DialogTrigger className="w-full">
            <div className="mt-10">
              <div className="flex items-center justify-between mb-4">
                <p className="text-black-100 font-medium">Add Required Skills</p>
                <p className="text-orange-100 font-medium">
                  {selectedSkills.length} items selected
                </p>
              </div>
              <div className="flex justify-between items-center shadow-md border p-2 sm:rounded-full rounded-2xl gap-3">
                <p className="text-gray-100 font-normal ps-3">For Example: SEO, Full-Stack etc</p>
                <div className="py-3 px-10 bg-orange-100 text-white rounded-full">Select</div>
              </div>
            </div>
          </DialogTrigger>

          <DialogContent className="max-w-[920px] h-[80vh] flex flex-col p-0 overflow-hidden">
            <Tabs defaultValue={jobCategories[0]?.value || ""} className="flex-1 flex flex-col">
              <div className="flex flex-1 overflow-hidden">
                {/* Categories Sidebar */}
                <div className="w-[280px] border-r border-gray-200 bg-white flex-shrink-0 overflow-y-auto">
                  <TabsList className="h-full flex flex-col items-start bg-transparent p-6 space-y-4">
                    <h3 className="text-lg font-bold text-gray-900 mb-2">Categories</h3>
                    {jobCategories.map(({ label, value }) => (
                      <TabsTrigger
                        key={value}
                        value={value}
                        className="w-full justify-start p-3 text-left text-gray-700 font-medium
                          data-[state=active]:text-orange-500 data-[state=active]:bg-orange-50
                          hover:bg-gray-50 rounded-lg transition-colors whitespace-normal break-words"
                      >
                        {label}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </div>

                {/* Skills Content */}
                <div className="flex-1 overflow-y-auto p-6 bg-gray-50">
                  {jobCategories.map(({ value }) => (
                    <TabsContent key={value} value={value} className="m-0">
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                        {skillsEnum[value] &&
                          Array.isArray(skillsEnum[value]) &&
                          skillsEnum[value].map((skill: string) => (
                            <button
                              key={skill}
                              type="button"
                              className={`border-2 rounded-full px-4 py-2 text-sm whitespace-normal break-words ${
                                tempSelectedSkills.includes(skill)
                                  ? "bg-orange-500 text-white border-orange-500"
                                  : "bg-white text-gray-700 border-gray-300 hover:border-gray-400"
                              }`}
                              onClick={() => handleAddSkill(skill)}
                            >
                              {skill}
                            </button>
                          ))}
                      </div>
                    </TabsContent>
                  ))}
                </div>
              </div>

              {/* Footer Buttons */}
              <div className="sticky bottom-0 w-full bg-white border-t p-4 flex justify-end gap-4">
                <button
                  type="button"
                  onClick={handleCancelSkillSelection}
                  className="px-6 py-2 rounded-full border border-gray-300 text-gray-700
                    font-medium hover:border-gray-400 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleConfirmSkillSelection}
                  className="px-6 py-2 rounded-full bg-orange-500 text-white
                    font-medium hover:bg-orange-600 transition-colors"
                >
                  Confirm Selection
                </button>
              </div>
            </Tabs>
          </DialogContent>
        </Dialog>

        {/* Selected Skills Display */}
        <div className="flex flex-wrap gap-4 mt-10">
          {selectedSkills.map((skill) => (
            <button
              key={skill}
              type="button"
              className="border-2 rounded-full text-orange-100 border-orange-100 bg-offWhite-100 text-base flex items-center py-2 px-4 gap-2"
              onClick={() => handleRemoveSkill(skill)}
            >
              {skill}
              <X className="w-4 h-4" />
            </button>
          ))}
        </div>
      </div>

      <div className="flex gap-4">
        <button
          type="button"
          onClick={() => router.push("/company-dashboard/all-jobs")}
          className="bg-gray-200 text-gray-800 rounded-full px-10 py-4 text-lg"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isPending}
          className="bg-orange-100 text-white rounded-full px-10 py-4 text-lg disabled:opacity-70"
        >
          {isPending
            ? jobId
              ? "Updating Job..."
              : "Posting Job..."
            : jobId
              ? "Update Job"
              : "Post Job"}
        </button>
      </div>
    </form>
  );
}
