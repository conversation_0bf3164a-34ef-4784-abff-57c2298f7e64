"use client";

import React, { useState } from "react";
import DeleteJobModal from "../DeleteJobModal";
import CompanyJobCardtwo from "@/components/Cards/CompanyJobCardtwo";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useDeleteJob, useUpdateJobStatus } from "@/hooks/useMutation";
import { useGetJobById } from "@/hooks/useQuery";
import { formatDate } from "@/lib/utils";

const JobDetail = ({ jobId }: { jobId: string }) => {
  const [jobToDelete, setJobToDelete] = useState<string | null>(null);
  const { mutate: deleteJob } = useDeleteJob();

  const { data: jobData, isLoading: isJobLoading } = useGetJobById(jobId || "", {
    enabled: !!jobId,
  });
  const { mutate: updateJobStatus, isPending } = useUpdateJobStatus();

  const handleDelete = (jobId: string) => {
    setJobToDelete(jobId); // Open confirmation modal
  };
  const cancelDelete = () => {
    setJobToDelete(null); // Close modal without deleting
  };
  const toggleJobStatus = (jobId: string, isJobActive: boolean) => {
    updateJobStatus({ jobId, isJobActive });
  };
  const confirmDelete = () => {
    if (jobToDelete) {
      deleteJob(jobToDelete, {
        onSuccess: () => {
          setJobToDelete(null); // Close modal after successful deletion
        },
      });
    }
  };

  if (isJobLoading) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500">Loading job details...</p>
      </div>
    );
  }

  const job = jobData?.data;
  return (
    <>
      <CompanyJobCardtwo
        imageUrl={DEFAULT_IMAGE}
        jobTitle={job?.jobTitle}
        companyName={job?.recruiterProfile?.companyName}
        cityName={job?.location.city}
        salaryRange={`$${job?.salaryRangeStart} - $${job?.salaryRangeEnd}`}
        jobType={job?.jobType}
        jobClosed={!job?.isJobActive}
        handleDelete={() => handleDelete(job?._id || "")}
        toggleJobStatus={() => toggleJobStatus(job?._id || "", !job?.isJobActive)}
        editJobLink={`/company-dashboard/post-job/${job?._id}`}
        viewJobLink={`/company-dashboard/all-jobs/${job?._id}`}
        category={job?.jobCategory}
        dateCreated={formatDate(job?.createdAt || "")}
        expireDate={formatDate(job?.applicationDeadline || "")}
        shortlistedApplicants={job?.shortlistedApplicants || 0}
        totalApplicants={job?.totalApplicants || 0}
        isDetailPage={true}
        isLoading={isPending}
      />
      {jobToDelete && <DeleteJobModal cancelDelete={cancelDelete} confirmDelete={confirmDelete} />}
    </>
  );
};

export default JobDetail;
