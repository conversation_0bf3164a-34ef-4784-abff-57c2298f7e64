"use client";
import { useQueryClient } from "@tanstack/react-query";
import { Eye, EyeOff } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { toast } from "sonner";
import { PrimaryHeading } from "@/components/Headings/PrimaryHeading";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useLogin } from "@/hooks/useMutation";
import { useUserStore } from "@/store/useUserStore";
import { ApiError, UserRole } from "@/types/common.types";
import { ILoginRequestDto } from "@/types/mutation.types";

const inputStyles = "h-12 px-4 rounded-full border border-[#737373] w-full";

export default function LoginForm() {
  const [showPassword, setShowPassword] = useState(false);
  const setToken = useUserStore((state) => state.setToken);
  const setCurrentUser = useUserStore((state) => state.setCurrentUser);
  // const router = useRouter();
  const queryClient = useQueryClient();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ILoginRequestDto>();
  const router = useRouter();
  const { mutateAsync: login, isPending } = useLogin({
    onSuccess: (data) => {
      toast("Login Successful!");
      if (data.data.user.role === UserRole.RECRUITER) {
        router.push("/company-dashboard/all-jobs");
      } else if (data.data.user.role === UserRole.JOBSEEKER) {
        router.push("/dashboard/applied-jobs");
      }
      setToken(data.data.token);
      setCurrentUser(data.data.user);
      console.log(data.data.user, "data.data.userdata.data.user");
      queryClient.invalidateQueries({ queryKey: ["get-current-user"] });
      router.push("/dashboard/applied-jobs");
    },
    onError: (error) => {
      toast((error as ApiError).response?.data.message || "Login failed");
    },
  });

  const onSubmit: SubmitHandler<ILoginRequestDto> = async (data) => {
    await login(data);
  };

  return (
    <div>
      <div className="text-center mb-6">
        <PrimaryHeading>
          <span>Login</span>
        </PrimaryHeading>
        <p className="text-gray-100 text-lg mt-3">Lorem ipsum dolor sit amet adipiscing elit.</p>
      </div>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email" className="text-black-100">
            Email
          </Label>
          <Input
            id="email"
            type="email"
            className={inputStyles}
            placeholder="Enter your email"
            autoComplete="email" // Added autoComplete for email
            {...register("email", {
              required: "Email is required",
              pattern: {
                value: /\S+@\S+\.\S+/,
                message: "Invalid email address",
              },
            })}
          />
          {errors.email && <p className="text-red-500 text-sm">{errors.email.message}</p>}
        </div>
        <div className="space-y-2">
          <Label htmlFor="password" className="text-black-100">
            Password
          </Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              className={inputStyles}
              placeholder="Enter your password"
              autoComplete="current-password" // Added autoComplete for password
              {...register("password", {
                required: "Password is required",
                minLength: {
                  value: 8,
                  message: "Password must be at least 8 characters",
                },
              })}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>
          {errors.password && <p className="text-red-500 text-sm">{errors.password.message}</p>}
        </div>
        <div className="text-right">
          <Link href={"/forget-password"} className="text-gray-100">
            Forgot Password
          </Link>
        </div>
        <Button
          type="submit"
          className="w-full bg-orange-100 text-base h-[48px] rounded-full"
          disabled={isPending}
        >
          {isPending ? "Logging in..." : "Log In"}
        </Button>
      </form>
      <div className="text-center mt-8">
        <p className="text-base text-gray-100 font-medium">
          Don&quot;t have an account?{" "}
          <Link className="text-orange-100" href={"/sign-up"}>
            Sign Up
          </Link>{" "}
        </p>
      </div>
    </div>
  );
}
