"use client";

import { Suspense, use } from "react";
import JobDetail from "@/components/Cards/JobDetail";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useGetJobById } from "@/hooks/useQuery";
import { formatDate } from "@/lib/utils";

export default function ShortlistedJobsDetailPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = use(params);
  const { data, isLoading, isError } = useGetJobById(slug || "", {
    enabled: !!slug,
  });

  const job = data?.data;

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px] py-20">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-100"></div>
      </div>
    );
  }

  if (isError || !job) {
    return (
      <div className="flex justify-center items-center min-h-[400px] py-20">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-500 mb-4">Error Loading Job</h2>
          <p className="text-gray-600">Unable to load the job details. Please try again later.</p>
        </div>
      </div>
    );
  }

  // Format salary range
  const salaryRange = `$${job.salaryRangeStart} - $${job.salaryRangeEnd}`;

  // Format job category
  const category = job.jobCategory.replace(/_/g, " ");

  // Format job type
  const jobType = job.jobType.replace(/_/g, " ");

  // Format experience level
  const experienceLevel = job.experienceLevel.replace(/_/g, " ");

  // Format qualification
  const qualification = job.qualification.replace(/_/g, " ");

  // Format career level
  const careerLevel = job.careerLevel.replace(/_/g, " ");

  // Split responsibilities and skills into arrays
  const keyResponsibilities = job.keyResponsibilities
    ? job.keyResponsibilities
        .split(".")
        .filter((item) => item.trim() !== "")
        .map((item) => item.trim() + ".")
    : [];

  const skillsExperience = job.skillsAndExperience
    ? job.skillsAndExperience
        .split(".")
        .filter((item) => item.trim() !== "")
        .map((item) => item.trim() + ".")
    : [];

  return (
    <Suspense fallback={<div className="animate-pulse h-[400px] bg-gray-100 rounded-lg"></div>}>
      <JobDetail
        jobId={job._id}
        onApplySuccess={() => {}}
        category={category}
        cityName={job.location.city}
        companyName={job.recruiterProfile?.companyName || "Company"}
        deadline={formatDate(job.applicationDeadline)}
        imageUrl={job.recruiterProfile?.profilePicture || DEFAULT_IMAGE}
        jobTitle={job.jobTitle}
        jobType={jobType}
        salaryRange={salaryRange}
        datePosted={formatDate(job.createdAt)}
        location={job.location.formattedAddress}
        experience={experienceLevel}
        qualification={qualification}
        careerLevel={careerLevel}
        jobDescription={job.jobDescription}
        keyResponsibilities={keyResponsibilities}
        skillsExperience={skillsExperience}
        skillsTags={job.skillsTag || []}
        isJobDetailINDashboard
        isApplyNowButton={false}
        premiumExpireAt={job.premiumExpireAt}
        isSavedButtonShow={false}
      />
    </Suspense>
  );
}
