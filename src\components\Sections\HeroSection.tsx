import Image from "next/image";
import React from "react";
import HeroCountCard from "../Cards/HeroCountCard";
import IncompleteProfileCard from "../Cards/IncompleteProfileCard";
import JobCardHero from "../Cards/JobCardHero";
import { BannerHeading } from "../Headings/BannerHeading";
import { BagIcon, GlobeIcon } from "../Icons";
import SearchBar from "./SearchBar";

const HeroSection = () => {
  return (
    <section className="lg:py-36 py-20 relative">
      <div className="absolute w-full bottom-0 left-0 z-[-1]">
        <Image alt="" src={"/images/spiral.png"} width={837} height={1920} className="w-full" />
      </div>
      <div className="container mx-auto">
        <div className="flex items-center">
          <div className="w-[30%] hidden lg:block">
            <div className="mb-20">
              <HeroCountCard icon={<GlobeIcon />} text="Companies" title="60+" />
            </div>
            <IncompleteProfileCard />
          </div>
          <div className="text-center lg:px-20">
            <Image
              src={"/images/provile.png"}
              alt="Privider image"
              className="mx-auto"
              width={168}
              height={60}
            />
            <div className="mb-10 mt-5">
              <span className="inline-flex gap-x-4 border border-gray-100 rounded-full px-4 py-2 mt-4">
                <span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke="#007AB5"
                      d="M10.861 3.363C11.368 2.454 11.621 2 12 2s.632.454 1.139 1.363l.13.235c.145.259.217.388.329.473s.252.117.532.18l.254.058c.984.222 1.476.334 1.593.71s-.218.769-.889 1.553l-.174.203c-.19.223-.285.334-.328.472s-.029.287 0 .584l.026.27c.102 1.047.152 1.57-.154 1.803s-.767.02-1.688-.404l-.239-.11c-.261-.12-.392-.18-.531-.18s-.27.06-.531.18l-.239.11c-.92.425-1.382.637-1.688.404s-.256-.756-.154-1.802l.026-.271c.029-.297.043-.446 0-.584s-.138-.25-.328-.472l-.174-.203c-.67-.784-1.006-1.177-.889-1.553s.609-.488 1.593-.71l.254-.058c.28-.063.42-.095.532-.18s.184-.214.328-.473zm8.569 4.319c.254-.455.38-.682.57-.682s.316.227.57.682l.065.117c.072.13.108.194.164.237s.126.058.266.09l.127.028c.492.112.738.167.796.356s-.109.384-.444.776l-.087.101c-.095.112-.143.168-.164.237s-.014.143 0 .292l.013.135c.05.523.076.785-.077.901s-.383.01-.844-.202l-.12-.055c-.13-.06-.196-.09-.265-.09-.07 0-.135.03-.266.09l-.119.055c-.46.212-.69.318-.844.202-.153-.116-.128-.378-.077-.901l.013-.135c.014-.15.022-.224 0-.292-.021-.07-.069-.125-.164-.237l-.087-.101c-.335-.392-.503-.588-.444-.776s.304-.244.796-.356l.127-.028c.14-.032.21-.048.266-.09.056-.043.092-.108.164-.237zm-16 0C3.685 7.227 3.81 7 4 7s.316.227.57.682l.065.117c.072.13.108.194.164.237s.126.058.266.09l.127.028c.492.112.738.167.797.356.058.188-.11.384-.445.776l-.087.101c-.095.112-.143.168-.164.237s-.014.143 0 .292l.013.135c.05.523.076.785-.077.901s-.384.01-.844-.202l-.12-.055c-.13-.06-.196-.09-.265-.09-.07 0-.135.03-.266.09l-.119.055c-.46.212-.69.318-.844.202-.153-.116-.128-.378-.077-.901l.013-.135c.014-.15.022-.224 0-.292-.021-.07-.069-.125-.164-.237l-.087-.101c-.335-.392-.503-.588-.445-.776.06-.189.305-.244.797-.356l.127-.028c.14-.032.21-.048.266-.09.056-.043.092-.108.164-.237z"
                    ></path>
                    <path
                      stroke="#007AB5"
                      strokeLinecap="round"
                      d="M5 20.388h2.26c1.01 0 2.033.106 3.016.308 1.755.36 3.56.4 5.33.118.868-.14 1.72-.355 2.492-.727.696-.337 1.549-.81 2.122-1.341.572-.53 1.168-1.397 1.59-2.075.364-.582.188-1.295-.386-1.728a1.89 1.89 0 0 0-2.22 0l-1.807 1.365c-.7.53-1.465 1.017-2.376 1.162q-.165.026-.345.047m0 0-.11.012m.11-.012a1 1 0 0 0 .427-.24 1.49 1.49 0 0 0 .126-2.134 1.9 1.9 0 0 0-.45-.367c-2.797-1.669-7.15-.398-9.779 1.467m9.676 1.274a.5.5 0 0 1-.11.012m0 0q-.906.09-1.814.004"
                    ></path>
                    <path
                      stroke="#007AB5"
                      d="M5 15.5a1.5 1.5 0 0 0-3 0v5a1.5 1.5 0 0 0 3 0z"
                    ></path>
                  </svg>
                </span>
                <span className="text-blue-100">Best Job Soluton Platform</span>
              </span>
            </div>
            <BannerHeading>
              Connecting Top Talent with Leading <span>Opportunities</span>
            </BannerHeading>
            <p className="mb-10 mt-10 font-medium text-2xl leading-9 text-gray-100">
              Join YesJobs to find your dream role or build your dream team with ease.
            </p>
            <SearchBar />
          </div>
          <div className="w-[30%] hidden lg:block">
            <div className="mb-20">
              <HeroCountCard icon={<BagIcon />} text="Job Available " title="20K+" />
            </div>
            <JobCardHero
              companyName="Nexflow"
              imageUrl="/images/company-logo.png"
              jobTags={["UI/UX", "Full time"]}
              jobTitle="UI/UX Designer"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
