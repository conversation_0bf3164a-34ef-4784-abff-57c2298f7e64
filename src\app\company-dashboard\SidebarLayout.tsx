"use client";
import React from "react";
import DashboardSidebar from "@/components/Sections/DashboardSidebar";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useGetCompanyProfile } from "@/hooks/useQuery";

const SidebarLayout = () => {
  const { data } = useGetCompanyProfile();
  const userProfile = data?.data.companyProfile;
  return (
    <>
      <DashboardSidebar
        candidateDesignation={userProfile?.companyABN ? userProfile?.companyABN.toString() : ""}
        candidateName={userProfile?.companyName ? userProfile?.companyName : ""}
        image={userProfile?.profilePicture ? userProfile?.profilePicture : DEFAULT_IMAGE}
        CompanyDashboard={true}
        CandidateDashboard={false}
        getFeatured={false}
      />
    </>
  );
};

export default SidebarLayout;
