"use client";

import { format } from "date-fns";
// import { Search, SlidersHorizontal } from "lucide-react";
import React from "react";

// import { Button } from "@/components/ui/button";
// import {
//   DropdownMenu,
//   DropdownMenuCheckboxItem,
//   DropdownMenuContent,
//   DropdownMenuLabel,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu";
// import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useGetPayments } from "@/hooks/useQuery";

const TransactionHistoryTable = () => {
  const { data, isLoading, isError, error } = useGetPayments();

  if (isLoading) {
    return (
      <div className="border border-gray-300 rounded-2xl p-6 text-center">
        Loading transaction history...
      </div>
    );
  }

  if (isError) {
    return (
      <div className="border border-gray-300 rounded-2xl p-6 text-center text-red-500">
        Error: {error?.message || "Failed to fetch transaction history."}
      </div>
    );
  }

  const payments = data?.data?.payments || [];

  return (
    <div className="border border-gray-300 rounded-2xl">
      <div className="md:flex justify-between p-6">
        <div className="mb-3 md:mb-0">
          <h2 className="text-black-100 text-2xl font-bold">Transaction History</h2>
        </div>
        {/* <div className="flex gap-2  ml-auto ">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2  h-4 w-4" />
            <Input
              type="search"
              placeholder="Search"
              className="pl-10 bg-white border border-gray-100 text-base rounded-full"
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="bg-white border border-gray-100 rounded-full gap-3"
              >
                <SlidersHorizontal className="h-4 w-4" />
                Filters
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Filter By</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem checked>Most Recent</DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem>Most Popular</DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem>Price: Low to High</DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem>Price: High to Low</DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div> */}
      </div>
      <Table className="border-t border-gray-300">
        <TableHeader>
          <TableRow>
            <TableHead className="text-orange-100 font-bold p-5">Plan Name</TableHead>
            <TableHead className="text-orange-100 font-bold p-5">Purchased Date</TableHead>
            <TableHead className="text-orange-100 font-bold p-5">Expire Date</TableHead>
            <TableHead className="text-orange-100 font-bold p-5">Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {payments.length > 0 ? (
            payments.map((payment) => (
              <TableRow key={payment._id}>
                <TableCell className="p-5">{payment.details.type}</TableCell>
                <TableCell className="p-5">
                  {format(new Date(payment.createdAt), "MMM dd, yyyy")}
                </TableCell>
                <TableCell className="p-5">
                  {payment.details.expiresAt
                    ? format(new Date(payment.details.expiresAt), "MMM dd, yyyy")
                    : "N/A"}
                </TableCell>
                <TableCell className="p-5 capitalize">{payment.paymentStatus}</TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={4} className="h-24 text-center">
                No transaction history found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default TransactionHistoryTable;
