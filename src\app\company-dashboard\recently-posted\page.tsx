"use client";

import { useState, useEffect } from "react";
import DeleteJobModal from "../all-jobs/DeleteJobModal";
import CompanyJobCardtwo from "@/components/Cards/CompanyJobCardtwo";
import Pagination from "@/components/Pagination/Pagination";
import JobNavigation from "@/components/Sections/JobNavigation";
import RecentlyPostedHeader from "@/components/Sections/RecentlyPostedHeader";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useDeleteJob, useUpdateJobStatus } from "@/hooks/useMutation";
import { useGetRecentJobs } from "@/hooks/useQuery";
import { formatDate } from "@/lib/utils";
const navItems = [
  { href: "/company-dashboard/all-jobs", label: "All Jobs" },
  { href: "/company-dashboard/recently-posted", label: "Recently Posted" },
  { href: "/company-dashboard/saved-candidates", label: "Saved Candidates" },
];

export default function RecentlyPostedJob() {
  const { mutate: deleteJob } = useDeleteJob();

  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [jobToDelete, setJobToDelete] = useState<string | null>(null);
  const { data, isLoading, isError, refetch } = useGetRecentJobs({ page, limit });
  const { mutate: updateJobStatus, isPending } = useUpdateJobStatus();

  const jobs = data?.data?.jobs || [];
  const pagination = data?.data?.pagination;

  useEffect(() => {
    refetch();
  }, [page, refetch]);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const toggleJobStatus = (jobId: string, isJobActive: boolean) => {
    updateJobStatus({ jobId, isJobActive });
  };

  const handleDelete = (jobId: string) => {
    setJobToDelete(jobId); // Open confirmation modal
  };
  const cancelDelete = () => {
    setJobToDelete(null); // Close modal without deleting
  };

  const confirmDelete = () => {
    if (jobToDelete) {
      deleteJob(jobToDelete, {
        onSuccess: () => {
          setJobToDelete(null); // Close modal after successful deletion
        },
      });
    }
  };

  if (isLoading) {
    return (
      <>
        <RecentlyPostedHeader />
        <div className="space-y-6">
          <JobNavigation navItems={navItems} />
          <div className="flex justify-center items-center py-10">
            <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900"></div>
            <p className="ml-3 text-gray-500">Loading jobs...</p>
          </div>
        </div>
      </>
    );
  }

  if (isError) {
    return (
      <>
        <RecentlyPostedHeader />
        <div className="space-y-6">
          <JobNavigation navItems={navItems} />
          <div className="p-6 bg-red-50 rounded-lg border border-red-200 text-red-700">
            <p>Failed to load jobs. Please try again later.</p>
          </div>
        </div>
      </>
    );
  }

  if (!jobs.length) {
    return (
      <>
        <RecentlyPostedHeader />
        <div className="space-y-6">
          <JobNavigation navItems={navItems} />
          <div className="p-6 bg-gray-50 rounded-lg border border-gray-200 text-gray-700">
            <p>No recent jobs found. Post a new job to get started!</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <RecentlyPostedHeader />
      <div className="space-y-6">
        <JobNavigation navItems={navItems} />

        {jobs.map((job) => (
          <CompanyJobCardtwo
            key={job._id}
            imageUrl={DEFAULT_IMAGE}
            jobTitle={job.jobTitle}
            companyName={job.recruiterProfile?.companyProfile?.companyName || "Company"}
            category={job.jobCategory}
            jobType={job.jobType}
            cityName={job.location?.city || job.location?.formattedAddress?.split(",")[0] || ""}
            salaryRange={`$${job.salaryRangeStart} - $${job.salaryRangeEnd}`}
            dateCreated={formatDate(job.createdAt)}
            expireDate={formatDate(job.applicationDeadline)}
            jobClosed={!job.isJobActive}
            toggleJobStatus={() => toggleJobStatus(job._id, !job.isJobActive)}
            editJobLink={`/company-dashboard/post-job/${job._id}`}
            viewJobLink={`/company-dashboard/all-jobs/${job._id}`}
            shortlistedApplicants={job.shortlistedApplicantsCount}
            totalApplicants={job.applicantsCount}
            isLoading={isPending}
            handleDelete={() => handleDelete(job._id)}
            isDeleted={job.isDeleted}
          />
        ))}

        {pagination && pagination.pages > 1 && (
          <div className="flex justify-center mt-8">
            <Pagination
              currentPage={page}
              totalPages={pagination.pages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>
      {jobToDelete && <DeleteJobModal cancelDelete={cancelDelete} confirmDelete={confirmDelete} />}
    </>
  );
}
