"use client";
import DashboardSidebar from "@/components/Sections/DashboardSidebar";
import FullPageLoader from "@/components/ui/FullPageLoader";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useGetCurrentUser, useGetJobSeekerProfile } from "@/hooks/useQuery";
import { useUserStore } from "@/store/useUserStore";

const SidebarLayout = ({ children }: { children: React.ReactNode }) => {
  const { currentUser, hasHydrated } = useUserStore();
  const { data: userData } = useGetCurrentUser({
    enabled: !!currentUser && hasHydrated,
  });
  const { data, isLoading } = useGetJobSeekerProfile();
  const skills = data?.data?.skills;

  if (!hasHydrated) {
    return <FullPageLoader message="Loading..." />;
  }

  return (
    <div className="container mx-auto py-14">
      <div className="lg:flex gap-x-10">
        <DashboardSidebar
          candidateDesignation={userData?.data?.designation || ""}
          candidateName={
            isLoading
              ? "Loading..."
              : `${userData?.data?.firstName || ""} ${userData?.data?.lastName || ""}`
          }
          getFeatured={true}
          image={userData?.data?.profilePicture || DEFAULT_IMAGE}
          candidateSkills={isLoading ? ["Loading..."] : skills}
          CandidateDashboard={true}
          CompanyDashboard={false}
          // proMembershipEndsAt={data?.data?.proMembershipEndsAt}
          proMembershipEndsAt={userData?.data.proTrialEndsAt}
          proTrialEndsAt={userData?.data.proMembershipEndsAt}
        />
        <div className="border border-gray-300 lg:w-[calc(100%-350px)] rounded-[18px] p-7">
          {children}
        </div>
      </div>
    </div>
  );
};

export default SidebarLayout;
