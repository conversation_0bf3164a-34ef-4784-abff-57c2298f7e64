import React from "react";

interface ICandidateDetailInfo {
  title: string;
  date: string;
  description: string;
  descriptionList?: string[];
  icon: React.ReactNode;
}

const CandidateDetailInfo = ({
  title,
  date,
  description,
  descriptionList,
  icon,
}: ICandidateDetailInfo) => {
  return (
    <div className="flex">
      <div>{icon}</div>
      <div className="ml-3">
        <h3 className="text-black-100 font-bold text-lg">{title}</h3>
        <p className="text-sm text-gray-100 font-medium my-2">{date}</p>
        <h4 className="text-black-100 font-medium">{description}</h4>
        {descriptionList && (
          <ul className="mt-4">
            {descriptionList?.map((item, index) => (
              <li
                key={index}
                className="text-base font-normal text-gray-100 mb-3 flex items-center gap-x-2"
              >
                {/* <span className="w-[5px] h-[5px] bg-gray-100 rounded-full flex justify-center items-center"></span>{" "} */}
                {item}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default CandidateDetailInfo;
