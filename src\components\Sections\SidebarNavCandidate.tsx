import { ChevronRight } from "lucide-react";
import Link from "next/link";
import React from "react";

const SidebarNavCandidate = () => {
  return (
    <>
      <ul className="space-y-4">
        <li>
          <Link
            href={"/dashboard/applied-jobs"}
            className="flex justify-between text-blue-100 font-bold"
          >
            <span>Applied Jobs</span>
            {/* <span>1</span> */}
          </Link>
        </li>
        <li>
          <Link
            href={"/dashboard/shortlisted-jobs"}
            className="flex justify-between text-blue-100 font-bold"
          >
            <span>Shortlisted Jobs</span>
            {/* <span>1</span> */}
          </Link>
        </li>
        <li>
          <Link
            href={"/dashboard/saved-jobs"}
            className="flex justify-between text-blue-100 font-bold"
          >
            <span>Saved Jobs</span>
            {/* <span>1</span> */}
          </Link>
        </li>
      </ul>
      <hr className="my-5" />
      <ul className="space-y-4">
        <li>
          <Link
            href={"/dashboard/applied-jobs"}
            className="flex justify-between text-black-100 font-medium"
          >
            <span>Dashboard</span>
            <span>
              {" "}
              <ChevronRight />{" "}
            </span>
          </Link>
        </li>
        <li>
          <Link
            href={"/my-resume/academic-experience"}
            className="flex justify-between text-black-100 font-medium"
          >
            <span>My Resume</span>
            <span>
              {" "}
              <ChevronRight />{" "}
            </span>
          </Link>
        </li>
        <li>
          <Link
            href={"/settings/my-profile"}
            className="flex justify-between text-black-100 font-medium"
          >
            <span>Settings</span>
            <span>
              {" "}
              <ChevronRight />{" "}
            </span>
          </Link>
        </li>
      </ul>
    </>
  );
};

export default SidebarNavCandidate;
