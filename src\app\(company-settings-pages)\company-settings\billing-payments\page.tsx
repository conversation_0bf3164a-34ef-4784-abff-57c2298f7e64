import BillingPaymentsCard from "@/components/Cards/BillingPaymentsCard";
import TransactionHistoryTable from "@/components/Cards/TransactionHistoryTable";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
export default function BillingPayments() {
  return (
    <>
      <Breadcrumb className="mb-8">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/applied-jobs">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Settings</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <h2 className="text-orange-100 text-3xl font-bold mb-10">Billing & Payments</h2>
      <div className="mb-10">
        <BillingPaymentsCard />
      </div>
      <div className="mt-10">
        <TransactionHistoryTable />
      </div>
    </>
  );
}
