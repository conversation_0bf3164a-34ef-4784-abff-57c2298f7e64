"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import FullPageLoader from "@/components/ui/FullPageLoader";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useUploadCV, useExtractCVDetails } from "@/hooks/useMutation";
import { ApiError } from "@/types/common.types";
import { IUploadCVResponse, IOCRExtractRequestDto } from "@/types/mutation.types";

const CompleteYourCV = () => {
  const router = useRouter();
  const [resumeOption, setResumeOption] = useState("manual");
  const [isUploading, setIsUploading] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);

  const { mutate: uploadCV } = useUploadCV();
  const { mutate: extractCVDetails } = useExtractCVDetails({
    onSuccess: (response) => {
      setIsExtracting(false);
      if (response.success) {
        toast.success(response.message || "CV details extracted successfully");
        router.push("?stepId=about-yourself");
      } else {
        toast.error(response.message || "Failed to extract CV details");
      }
    },
    onError: (error: ApiError) => {
      setIsExtracting(false);
      toast.error(error.response?.data?.message || "Failed to extract CV details");
    },
  });

  const handleFileUpload = (file: File | null) => {
    if (!file) {
      toast.error("Please select a file");
      return;
    }

    if (file.type !== "application/pdf") {
      toast.error("Only PDF files are allowed");
      return;
    }

    const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    if (file.size > MAX_FILE_SIZE) {
      toast.error("File size should be less than 5MB");
      return;
    }

    setIsUploading(true);
    uploadCV(
      {
        file,
        options: {
          onProgress: (_progress: number) => {
            // Progress tracking for accessibility
            const progressEl = document.querySelector('[role="progressbar"]');
            if (progressEl) {
              progressEl.setAttribute("aria-valuenow", _progress.toString());
            }
          },
        },
      },
      {
        onSuccess: (data: IOCRExtractRequestDto, _variables, _context) => {
          setIsUploading(false);
          const cvAttachments = (data as unknown as IUploadCVResponse).data?.cvAttachments;
          if (!cvAttachments?.length) {
            toast.error("No CV attachment found in response");
            return;
          }

          const latestCV = cvAttachments[cvAttachments.length - 1];
          if (!latestCV) {
            toast.error("Could not find the uploaded CV");
            return;
          }

          setIsExtracting(true);
          extractCVDetails({
            cvUrl: latestCV.cvUrl,
            cvName: latestCV.cvName,
            mimeType: latestCV.mimeType,
            fileSize: latestCV.fileSize,
            uploadedDate: latestCV.uploadedDate,
            s3Key: latestCV.s3Key,
          });
        },
        onError: (error: ApiError) => {
          setIsUploading(false);
          toast.error(error.response?.data?.message || "Failed to upload CV");
        },
      }
    );
  };

  const handleOptionSelect = (option: string) => {
    setResumeOption(option);
    if (option === "manual") {
      router.push("?stepId=about-yourself");
    }
  };

  return (
    <>
      {(isUploading || isExtracting) && (
        <FullPageLoader message={isUploading ? "CV Uploading..." : "CV Extracting..."} />
      )}
      <div>
        <h3 className="text-3xl font-semibold mb-2 text-blue-100">Complete Your Resume/CV</h3>
        <p className="text-gray-100 mb-10">
          Choose how you&apos;d like to add your Resume/CV details
        </p>
        <RadioGroup
          value={resumeOption}
          onValueChange={handleOptionSelect}
          className="grid xl:grid-cols-2 gap-10"
        >
          <div
            className={`relative flex items-center justify-center h-full flex-col p-6 gap-8 rounded-2xl border ${
              resumeOption === "manual" ? "border-orange-500" : "border-gray-300"
            }`}
          >
            <RadioGroupItem value="manual" id="manual" className="sr-only" />
            {resumeOption === "manual" && (
              <div className="absolute top-4 right-4 w-4 h-4 rounded-full bg-orange-500" />
            )}
            <Label htmlFor="manual" className="cursor-pointer w-full">
              <div className="space-y-4 mb-5">
                <Image src="/images/registration1.png" alt="" width={72} height={72} />
                <h4 className="text-3xl font-bold">Create Manually</h4>
                <p className="text-gray-100 leading-6">
                  Enter your information step by step to build a detailed and customized profile.
                </p>
              </div>
              <button
                type="button"
                onClick={() => router.push("?stepId=about-yourself")}
                className={`w-full font-bold py-4 px-10 rounded-full inline-flex items-center justify-center space-x-2 ${
                  resumeOption === "manual"
                    ? "bg-orange-100 text-white"
                    : "text-gray-100 bg-gray-300"
                }`}
              >
                Enter Manually
              </button>
            </Label>
          </div>

          <div
            className={`relative flex items-center justify-center h-full flex-col p-6 gap-8 rounded-2xl border ${
              resumeOption === "upload" ? "border-orange-500" : "border-gray-300"
            }`}
          >
            <RadioGroupItem value="upload" id="upload" className="sr-only" />
            {resumeOption === "upload" && (
              <div className="absolute top-4 right-4 w-4 h-4 rounded-full bg-orange-500" />
            )}
            <Label htmlFor="upload" className="cursor-pointer w-full">
              <div className="space-y-4 mb-5">
                <Image src="/images/cv1.png" alt="" width={72} height={72} />
                <h4 className="text-3xl font-bold">Upload Your resume/cv</h4>
                <p className="text-gray-100 leading-6">
                  Save time! Upload your resume/cv, and we&apos;ll automatically extract your
                  details to fill in your profile.
                </p>
              </div>
              <div>
                <div className="text-gray-700 text-base font-normal">Upload CV</div>
                <div className="mb-7 flex items-center justify-center h-[80px] mt-4 rounded-2xl border-2 border-dashed border-gray-300 bg-gray-50">
                  <input
                    type="file"
                    accept="application/pdf"
                    onChange={(e) => handleFileUpload(e.target.files?.[0] || null)}
                    className="absolute inset-0 opacity-0 cursor-pointer"
                    disabled={isUploading || isExtracting}
                  />
                  <div className="text-center">
                    <span className="mt-2 block text-sm font-semibold text-gray-600">
                      {isUploading
                        ? "Uploading CV..."
                        : isExtracting
                          ? "Extracting PDF..."
                          : "Upload CV (PDF only, max 5MB)"}
                    </span>
                  </div>
                </div>
              </div>
            </Label>
          </div>
        </RadioGroup>
      </div>
    </>
  );
};

export default CompleteYourCV;
